'use client';
import BasicTablePage from '@/components/form/BasicTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

const Awards = () => {
  const router = useRouter();
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['awards', page, limit, search],
    endPoint: `/awards${search.length > 2 ? `?name=${search}` : ''}`,
    params: { page, limit },
    enabled: search.length < 1 || search.length > 2
  });

  const columns = [
    { label: '#', field: 'index' },
    { label: 'Award Name', field: 'name' },
    { label: 'Module', field: 'module' },
    { label: 'Frequency', field: 'frequency' },
    { label: 'Status', field: 'status' },
    { label: 'Action', field: '' },
  ];

  const tableData = data?.items?.map((item, index) => ({
    index: (page - 1) * limit + index + 1,
    name: item.name,
    module: item.module,
    frequency: item.frequency,
    status: item.isActive ? (
      <span className="text-green-600 bg-green-100 px-3 py-1 rounded">
        Active
      </span>
    ) : (
      <span className="text-red-600 bg-red-100 px-3 py-1 rounded">
        Inactive
      </span>
    ),
  }));

  const actions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (val) => router.push(`/dashboard/awards/${data?.items[val]?.id}`),
    },
    {
      name: 'edit',
      className: 'text-gray-600',
      icon: 'material-symbols:edit-outline',
      onClick: (val) => router.push(`/dashboard/awards/edit/${data?.items[val]?.id}`),
    },
    {
      name: 'delete',
      className: 'text-red-600',
      icon: 'heroicons-outline:trash',
      onClick: async (val) => {
        await api.delete(`/awards/${data?.items[val]?.id}`);
        refetch();
      },
    },
  ];

  return (
    <div>
      <div className="max-w-72 relative mb-3">
        <input
          type="text"
          onChange={(e) => {
            const value = e.target.value;
            if (value.length > 2 || value.length === 0) {
              setSearch(value);
            }
          }}
          placeholder="Search awards..."
          className="w-full px-4 py-2 pr-8 border rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
        />
        <Icon
          icon="stash:search"
          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5"
        />
      </div>

      <BasicTablePage
        title="Award List"
        createButton="Add Award"
        createBtnLink="/dashboard/awards/add"
        columns={columns}
        actions={actions}
        data={tableData}
        loading={isLoading}
        currentPage={page}
        changePage={setPage}
        totalItems={data?.totalCount || 0}
        rowsPerPage={limit}
      />
    </div>
  );
};

export default Awards;
