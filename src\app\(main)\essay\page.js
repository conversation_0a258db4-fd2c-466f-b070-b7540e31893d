'use client';

import { useEffect, useCallback, useState } from 'react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';

import api from '@/lib/api';

import {
  setPreviewMode,
  updateCanvasItem,
  changeBackground,
  addImageToCanvas,
  resetCanvas,
  selectCanvasItems,
  setSelectedId,
} from '@/store/features/canvasSlice';

import {
  setSubject,
  setMessage,
  setSelectedSkin,
  setIsSaving,
  setIsLoading,
  setTodayEntry,
  setIsSkinModalOpen,
  setLayoutBackground,
  selectDiarySubject,
  selectDiaryMessage,
  selectSelectedSkin,
  selectIsSaving,
  selectIsLoading,
  selectTodayEntry,
  selectIsSkinModalOpen,
  selectLayoutBackground,
} from '@/store/features/diarySlice';
import SelectSkinModal from '../diary/_component/SelectSkinModal';
import StageSelector from '../diary/_component/StageSelector';
import DiaryForm from '../diary/_component/DiaryForm';
import StudentDiaryCanvas from '../diary/StudentDiaryCanvas';
import MessageModal from '../diary/_component/modalContents/MessageModal';

const applyTemplate = (
  templateData,
  diaryData,
  dispatch
) => {
  if (templateData.background) {
    dispatch(changeBackground(templateData.background));
  }

  if (templateData.items?.length) {
    templateData.items.forEach((item) => {
      if (item.type === 'text') {
        let contentForCanvas = item.content; // Start with template's default content
        let itemId = item.id;

        // Determine content for the CANVAS ITEM
        if (item.id === 'subject' || item.id.startsWith('subject')) {
          itemId = 'subject';
          // Use diary data's title for the canvas if it exists, otherwise use template content
          contentForCanvas = diaryData?.title || item.content;
          // *** REMOVE THIS LINE: setSubject(content); ***
          // The form's subject state is already set by fetchTodayEntry
        } else if (item.id === 'body' || item.id.startsWith('body')) {
          itemId = 'body';
          // Use diary data's content for the canvas if it exists, otherwise use template content
          contentForCanvas = diaryData?.content || item.content;
          // *** REMOVE THIS LINE: setMessage(content); ***
          // The form's message state is already set by fetchTodayEntry
        } else if (item.id === 'date' || item.id.startsWith('date')) {
          itemId = 'date';
          contentForCanvas = format(
            new Date(),
            item.dateFormat || 'dd MMM yyyy'
          );
        }

        dispatch({
          type: 'canvas/addTextItem', // This action specifically adds/updates items on the canvas
          payload: {
            ...item,
            id: itemId,
            content: contentForCanvas, // Use the determined content for the canvas item
          },
        });
      } else if (item.type === 'image') {
        dispatch(
          addImageToCanvas({
            id: `${item.id}-${Date.now()}`,
            imageSrc: item.image,
            styles: item.styles,
            zIndex: item.zIndex || 1,
          })
        );
      }
    });
  }
};

// Modify the handleSkinChange function:
const handleSkinChange = async (
  newSkin,
  dispatch,
  setSelectedSkin,
  setSubject,
  setMessage
) => {
  if (!newSkin.templateContent) {
    toast.error('This skin has no valid template content');
    return;
  }

  try {
    // 1. First reset everything
    dispatch(resetCanvas());
    setSubject('');
    setMessage('');

    // 2. Then set the new skin
    setSelectedSkin(newSkin);

    // 3. Parse and apply template
    const templateData = JSON.parse(newSkin.templateContent);

    // 4. Set background if exists
    if (templateData.background) {
      dispatch(changeBackground(templateData.background));
    }

    // 5. Process items with a small delay
    setTimeout(() => {
      if (templateData.items?.length) {
        templateData.items.forEach((item) => {
          if (item.type === 'text') {
            let content = item.content;
            let itemId = item.id;

            // Handle special fields
            if (item.id === 'subject' || item.id.startsWith('subject')) {
              itemId = 'subject';
              content = item.content;
              setSubject(content); // Update subject state
            } else if (item.id === 'body' || item.id.startsWith('body')) {
              itemId = 'body';
              content = item.content;
              setMessage(content); // Update message state
            } else if (item.id === 'date' || item.id.startsWith('date')) {
              itemId = 'date';
              content = format(new Date(), item.dateFormat || 'dd MMM yyyy');
            }

            // Add text item with full styles
            dispatch({
              type: 'canvas/addTextItem',
              payload: {
                ...item,
                id: itemId,
                content,
                styles: {
                  ...item.styles,
                  width: item.styles?.width || 300,
                  height: item.styles?.height || 40,
                  x: item.styles?.x || 50,
                  y: item.styles?.y || 20,
                },
              },
            });
          } else if (item.type === 'image') {
            dispatch(
              addImageToCanvas({
                id: `${item.id}-${Date.now()}`,
                imageSrc: item.image,
                styles: item.styles,
                zIndex: item.zIndex || 1,
              })
            );
          }
        });
      }

      // Force refresh
      dispatch(setPreviewMode(false));
      setTimeout(() => dispatch(setPreviewMode(true)), 50);
    }, 50);

    toast.success(`Skin "${newSkin.name}" applied successfully`);
  } catch (error) {
    console.error('Error applying skin template:', error);
    toast.error('Failed to apply skin template');
  }
};
export default function WriteEssay() {
  const router = useRouter();
  const dispatch = useDispatch();
  const essayId = useSearchParams().get('essayId');

  // Add state for selected stage template ID
  const [selectedStageTemplateId, setSelectedStageTemplateId] = useState(null);
  const [selectedStage, setSelectedStage] = useState(null);
  const [wordCount, setWordCount] = useState(0);

  // Selectors
  const today = format(new Date(), 'dd MMM yyyy');
  const canvasItems = useSelector(selectCanvasItems);

  // Diary state
  const subject = useSelector(selectDiarySubject);
  const message = useSelector(selectDiaryMessage);
  const selectedSkin = useSelector(selectSelectedSkin);
  const isSaving = useSelector(selectIsSaving);
  const isLoading = useSelector(selectIsLoading);
  const todayEntry = useSelector(selectTodayEntry);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const layoutBackground = useSelector(selectLayoutBackground);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(
    todayEntry?.hasGreeting === false
  );
  // Function to count words in a string
  const countWords = (text) => {
    if (!text || text.trim() === '') return 0;
    return text.trim().split(/\s+/).length;
  };
  // Detect browser zoom level
  useEffect(() => {
    const detectZoom = () => {
      const zoom =
        Math.round((window.outerWidth / window.innerWidth) * 100) / 100;
      setZoomLevel(zoom);
    };

    detectZoom();
    window.addEventListener('resize', detectZoom);

    return () => window.removeEventListener('resize', detectZoom);
  }, []);

  // Handle stage change
  const handleStageChange = useCallback(
    (stage) => {
    //   console.log('Selected stage:', stage);
      setSelectedStageTemplateId(stage.id);
      setSelectedStage(stage);

      // Update word count when stage changes
      setWordCount(countWords(message));
    },
    [message]
  );

  // Background color listener
  useEffect(() => {
    const storedColor = localStorage.getItem('diaryLayoutBackground');
    if (storedColor) {
      dispatch(setLayoutBackground(storedColor));
    }

    const handleBackgroundChange = (event) => {
      dispatch(setLayoutBackground(event.detail));
    };

    window.addEventListener('layoutBackgroundChanged', handleBackgroundChange);
    return () =>
      window.removeEventListener(
        'layoutBackgroundChanged',
        handleBackgroundChange
      );
  }, [dispatch]);

  // Enable preview mode on mount
  useEffect(() => {
    dispatch(setPreviewMode(true));
  }, [dispatch]);

  // Update word count whenever message changes
  useEffect(() => {
    setWordCount(countWords(message));
  }, [message]);

  // Listen for the openGreetingModal event
  useEffect(() => {
    const handleOpenGreetingModal = () => {
      setIsMessageModalOpen(true);
    };

    window.addEventListener('openGreetingModal', handleOpenGreetingModal);
    return () => {
      window.removeEventListener('openGreetingModal', handleOpenGreetingModal);
    };
  }, []);

  // Listen for the greetingSent event
  useEffect(() => {
    const handleGreetingSent = async (event) => {
      if (event.detail?.success) {
        // Refresh the diary entry data
        dispatch(setIsLoading(true));
      }
    };

    window.addEventListener('greetingSent', handleGreetingSent);
    return () => {
      window.removeEventListener('greetingSent', handleGreetingSent);
    };
  }, [dispatch]);

  // Initialize selected stage from API data when selectedStageTemplateId changes
  useEffect(() => {
    const fetchStageData = async () => {
      if (!selectedStageTemplateId) return;

      try {
        const response = await api.get('/diary/settings');
        if (response.success && response.data?.items) {
          const stage = response.data.items.find(
            (item) => item.id === selectedStageTemplateId
          );
          if (stage) {
            setSelectedStage(stage);
          }
        }
      } catch (error) {
        console.error('Error fetching stage data:', error);
      }
    };

    fetchStageData();
  }, [selectedStageTemplateId]);

  // Update canvas when form values change - FIXED VERSION
  useEffect(() => {
    if (isLoading) return;

    // Only update if the values are different from the canvas items
    const subjectItem = canvasItems.find(
      (item) => item.id === 'subject' || item.id.startsWith('subject-')
    );
    const bodyItem = canvasItems.find(
      (item) => item.id === 'body' || item.id.startsWith('body-')
    );
    const dateItem = canvasItems.find(
      (item) => item.id === 'date' || item.id.startsWith('date-')
    );

    // Helper function to safely compare and update
    const updateIfNeeded = (item, newContent, defaultValue) => {
      if (item && item.content !== (newContent || defaultValue)) {
        dispatch(
          updateCanvasItem({
            id: item.id,
            updates: { content: newContent || defaultValue },
          })
        );
      }
    };

    updateIfNeeded(subjectItem, subject, '');
    updateIfNeeded(bodyItem, message, '');
    updateIfNeeded(dateItem, today, today);
  }, [subject, message, today, isLoading, dispatch, canvasItems]);

  // Fetch today's entry
  useEffect(() => {
    const fetchTodayEntry = async () => {
      if (!isLoading && !essayId) return;

      try {
        const response = await api.get(`/student-essay/skins/${essayId}`);

        if (response.success && response.data) {
          const { data } = response;
          const essayData = data?.moduleDefaultSkin;
          
          dispatch(setTodayEntry(essayData));
          dispatch(setSubject(essayData.skin.name || ''));
          dispatch(setMessage(essayData.skin.description || ''));

          // Set the selected stage template ID from the diary entry
          if (essayData.settings?.settingsTemplateId) {
            setSelectedStageTemplateId(essayData.settings.settingsTemplateId);
          }

          if (essayData.skin) {
            dispatch(setSelectedSkin(essayData.skin));
            dispatch(resetCanvas());

            if (essayData.backgroundColor) {
              dispatch(changeBackground(essayData.backgroundColor));
            }

            if (essayData.skin.templateContent) {
              const templateData = JSON.parse(essayData.skin.templateContent);
              applyTemplate(templateData, essayData, dispatch);

              // Explicitly clear selection after applying template
              dispatch(setSelectedId(null));
            }
          } else {
            router.push('/select-skin');
          }
        } else if (!selectedSkin) {
          router.push('/select-skin');
        }
      } catch (error) {
        console.error('Error fetching diary entry:', error);
      } finally {
        dispatch(setIsLoading(false));
        // Ensure nothing is selected after loading completes
        dispatch(setSelectedId(null));
      }
    };

    fetchTodayEntry();
  }, []);

  // Handle save
  const handleSave = useCallback(async () => {
    if (!subject.trim() && !message.trim()) {
      toast.warning('Please enter either subject or content');
      return;
    }

    dispatch(setIsSaving(true));

    try {
      const payload = {
        title: subject,
        content: message,
        taskId: essayId || null,
      };

      let apiCall;

      // Check if entry exists
      if (todayEntry?.id) {
        // Check if status is "new" - if so, use the submit endpoint
        apiCall = api.post(`/student-essay/submit/task`, payload);
      } else {
        // If no entry exists yet, create a new one
        apiCall = api.post('/diary/entries', {
          ...payload,
          entryDate: format(new Date(), 'yyyy-MM-dd'),
        });
      }

      const response = await apiCall;

      if (response.success) {
        dispatch(setTodayEntry(response.data));

        // Show appropriate success message based on the action performed
        if (todayEntry?.status === 'new' && todayEntry?.id) {
          toast.success('Diary submitted successfully!');
        } else {
          toast.success('Diary saved successfully!');
        }
      }
    } catch (error) {
      console.error('Error saving diary entry:', error);
      toast.error('Failed to save diary entry. Please try again.');
    } finally {
      dispatch(setIsSaving(false));
    }
  }, [
    subject,
    message,
    selectedSkin,
    layoutBackground,
    todayEntry,
    dispatch,
    selectedStageTemplateId,
  ]);

  return (
    <>
      <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8 flex items-center gap-2">
        <div className="w-full min-h-[500px] bg-pink-100 p-2">
          {isLoading ? (
            <div className="flex items-center justify-center h-[500px]">
              <div className="text-center">
                <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mb-4"></div>
                <h2 className="text-xl font-semibold">Loading your diary...</h2>
              </div>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 items-center relative">
                <div className="bg-white h-full  p-2 overflow-hidden shadow-xl">
                  {/* Stage Selector with the selected stage template ID */}
                  <div className="mb-4">
                    <StageSelector
                      onStageChange={handleStageChange}
                      selectedTemplateId={selectedStageTemplateId}
                    />
                  </div>
                  <div className="w-full h-[500px] flex items-center justify-center overflow-hidden">
                    <div
                      className="canvas-container-wrapper"
                      style={{ width: '100%', height: '100%' }}
                    >
                      <StudentDiaryCanvas data={todayEntry} />
                    </div>
                  </div>
                  <div className="flex justify-center ">
                    {/* {todayEntry?.hasGreeting === false ? (
                      <button
                        onClick={() => setIsMessageModalOpen(true)}
                        className={`
                        text-black font-medium py-2 px-6 text-center rounded-full whitespace-nowrap
                        border-2 border-yellow-100
                        shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                        transition-all duration-300
                        bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                        relative pr-10
                        ring-2 ring-[#A36105]
                        `}
                      >
                        Continue
                      </button>
                    ) : ( */}
                      <button
                        onClick={handleSave}
                        disabled={isSaving}
                        className={`  text-black font-medium py-2 px-8  text-center rounded-full whitespace-nowrap
                            border-2 border-yellow-100
                            shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                            transition-all duration-300
                            bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                            relative pr-8
                            ring-2 ring-[#A36105] ${
                                isSaving
                                ? 'bg-gray-300 cursor-not-allowed'
                                : 'bg-yellow-400 hover:bg-yellow-300'
                            }`}
                        >
                        {isSaving
                          ? 'Saving...'
                          : todayEntry
                          ? 'Submit'
                          : 'Save'}
                      </button>
                    {/* )} */}
                  </div>
                </div>

                <div className="bg-white h-full p-2 overflow-hidden shadow-xl">
                  <DiaryForm
                    today={today}
                    todayEntry={todayEntry}
                    subject={subject}
                    message={message}
                    wordCount={wordCount}
                    selectedStage={selectedStage}
                  />
                </div>
              </div>
            </>
          )}

          <SelectSkinModal
            isOpen={isSkinModalOpen}
            onClose={() => dispatch(setIsSkinModalOpen(false))}
            onApply={(skin) =>
              handleSkinChange(
                skin,
                dispatch,
                (skin) => dispatch(setSelectedSkin(skin)),
                (content) => dispatch(setSubject(content)),
                (content) => dispatch(setMessage(content))
              )
            }
            currentSkinId={selectedSkin?.id}
          />
          <MessageModal
            isOpen={isMessageModalOpen}
            onClose={() => setIsMessageModalOpen(false)}
          />
        </div>
      </div>
    </>
  );
}
