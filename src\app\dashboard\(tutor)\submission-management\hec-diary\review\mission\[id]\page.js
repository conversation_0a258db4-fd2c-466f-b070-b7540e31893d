'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCanvas from '../../_components/DiaryCanvas';
import HecDiaryLayout from '../../_components/HecDiaryLayout';
import FeedbackModal from '../../_components/FeedbackModal';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';
import MissionReview from '../MissionReview';

const MissionDiaryReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [correctionText, setCorrection] = useState('');
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'missionDiary');

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Fetch mission diary entry data
  const { data, isLoading, error } = useDataFetch({
    queryKey: 'mission-diary-entry-review',
    endPoint: `/diary/tutor/missions/entries/${id}`,
    method: 'get',
    params: {},
    enabled: !!id,
  });

  // Initialize correction text with original content when data is loaded
  useEffect(() => {
    if (data?.content) {
      setCorrection(data.content);
    }
  }, [data?.content]);

  // Submit review function
  const submitReview = async () => {
    if (!score) {
      toast.error('Please provide a score');
      return;
    }

    try {
      const response = await api.post(
        `/tutor/diary/mission/entries/${id}/correction`,
        {
          correctionText,
          score: parseInt(score),
        }
      );

      if (response.success) {
        toast.success('Review submitted successfully');
        router.push(
          '/dashboard/submission-management/hec-diary?tab=missionDiary'
        );
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    }
  };

  const handleBack = () => {
    router.push('/dashboard/submission-management/hec-diary?tab=missionDiary');
  };

  if (isLoading) {
    return (
      <HecDiaryLayout activeTab={activeTab}>
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </HecDiaryLayout>
    );
  }

  if (error) {
    return (
      <HecDiaryLayout activeTab={activeTab}>
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          Error loading diary entry: {error.message}
        </div>
      </HecDiaryLayout>
    );
  }

  return (
    <HecDiaryLayout activeTab={activeTab}>
      <div className="flex justify-between items-center mb-2">
        <div className="flex gap-4 items-center">
          <h6 className="text-sm text-black font-medium ">
            View Mission Diary
          </h6>
          <h2 className="text-[#464646] font-normal">
            {data?.diary?.userName}
          </h2>
        </div>
      </div>

      <div className=" bg-[#FFF9FB] gap-2 p-3 rounded-lg shadow-xl">
        {/* Review Section */}
        <div className="">
          {/* Original Content */}
          <div className="mb-4 rounded-md shadow-lg p-4 bg-[#FCF8EF]">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-2xl font-semibold text-[#723F11] ">
                Hello English Coaching Mission Diary
              </h3>
            </div>
            <h6 className="text-[#314158] font-semibold text-lg mb-2">
              Instructions
            </h6>
            <p className="text-sm text-[#314158] font-medium">
              {data?.mission?.title}
            </p>
          </div>

          <div className="flex justify-between items-center mb-2">
            <h3 className="text-2xl font-semibold text-[#723F11] ">
              Student Submission
            </h3>
          </div>
          <div className="mb-4 rounded-md shadow-lg p-4 bg-white">
            <p className="text-sm text-[#314158] font-medium">
              {data?.content}
            </p>
          </div>

          <MissionReview data={data} entryId={id} />
        </div>
      </div>

   
    </HecDiaryLayout>
  );
};

export default MissionDiaryReviewPage;
