'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from "@/components/form/NewTablePage";

// DiaryGoal component that uses createBtnLink for navigation
const DiaryGoal = () => {
  // const router = useRouter(); // Not needed since we're using createBtnLink
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  
  // Dummy data for questions
  const dummyQuestions = [
    {
      id: '1',
      goalStage: '1',
      wordCount: 5,
    },
    {
      id: '2',
      goalStage: '1',
      wordCount: 5,
    },
  ];

  // Pagination logic
  const totalItems = dummyQuestions.length;
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedQuestions = dummyQuestions.slice(startIndex, endIndex);

  // Define columns for the table
  const columns = [
    {
      label: 'Goal Stage',
      field: 'goalStage',
    },
    {
      label: 'Word Count',
      field: 'wordCount',
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  // This function won't be used since we're using createBtnLink
  // const handleAddStage = () => {
  //   console.log('Navigating to add page...');
  //   router.push('/dashboard/module-management/hec-diary/diary-goal/add');
  // };
    
  return (
    <div className="container mx-auto p-4">
      <NewTablePage
        title="Today's Diary Goal"
        createButton="Add Stage"
        createBtnLink="/dashboard/module-management/hec-diary/diary-goal/add"
        columns={columns}
        data={paginatedQuestions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        // Remove openCreateModal since we're using createBtnLink
        // openCreateModal={handleAddStage}
        showCheckboxes={false}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
      />
    </div>
  );
};

export default DiaryGoal;