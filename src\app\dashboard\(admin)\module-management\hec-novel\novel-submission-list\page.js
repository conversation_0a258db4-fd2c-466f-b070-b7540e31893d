'use client';

import NewTablePage from "@/components/form/NewTablePage";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';

const NovelTopicList = () => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [novelTopics, setNovelTopics] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const router = useRouter();

  const actions = [
    {
      icon: 'material-symbols:visibility',
      className: 'text-blue-500 hover:text-blue-700',
      onClick: (row) => viewAwardDetails(row)
    }
  ];

  const columns = [
    { field: 'name', label: 'USER NAME', sortable: false },
    { field: 'rewardPoints', label: 'NOVEL TITLE', sortable: false },
    { field: 'rewardPo', label: 'SUBMISSION DATE', sortable: false }
  ];

  // Fetch novel topics
  const fetchNovelTopics = async () => {
    try {
      setIsLoading(true);
      const endpoint = `/admin/qa/questions?page=${currentPage}&limit=${rowsPerPage}`;
      
      console.log(`Fetching from: ${endpoint}`);
      
      const response = await api.get(endpoint);
      console.log('Raw API response:', response);
      
      // Data extraction to handle different response structures
      if (response?.data) {
        let items = [];
        let totalCount = 0;
        let totalPagesCount = 0;
        
        // Check different possible data structures
        if (response.data.items && Array.isArray(response.data.items)) {
          items = response.data.items;
          totalCount = response.data.totalCount || response.data.totalItems || 0;
          totalPagesCount = response.data.totalPages || 0;
        } else if (response.data.data) {
          if (response.data.data.items && Array.isArray(response.data.data.items)) {
            items = response.data.data.items;
            totalCount = response.data.data.totalCount || response.data.data.totalItems || 0;
            totalPagesCount = response.data.data.totalPages || 0;
          } else if (Array.isArray(response.data.data)) {
            items = response.data.data;
            totalCount = items.length;
            totalPagesCount = 1;
          }
        } else if (Array.isArray(response.data)) {
          items = response.data;
          totalCount = items.length;
          totalPagesCount = 1;
        }
        
        if (items.length > 0) {
          const formattedTopics = items.map(item => ({
            id: item.id,
            userName: item.userName || `${item.firstName || ''} ${item.lastName || ''}`.trim(),
            topicTitle: item.title || item.topic,
          }));
          
          setNovelTopics(formattedTopics);
          setTotalItems(totalCount);
          setTotalPages(totalPagesCount);
          setIsError(false);
          setErrorMessage('');
        } else {
          console.log('No items found in response');
          setNovelTopics([]);
          setTotalItems(0);
          setTotalPages(0);
          setIsError(false);
          setErrorMessage('');
        }
      } else {
        console.error('Unexpected data structure:', response);
        setIsError(true);
        setErrorMessage('Unexpected data structure received from API');
        setNovelTopics([]);
      }
    } catch (err) {
      console.error('Error fetching novel topics:', err);
      setIsError(true);
      setErrorMessage(err.message || 'An error occurred while fetching novel topics');
      setNovelTopics([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNovelTopics();
  }, [currentPage, rowsPerPage]);

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6"> Novel Submission List</h1>
      
      {/* Content Area - Novel Topics */}
      <div className="bg-white rounded-lg shadow">
        <NewTablePage
          title=""
          data={novelTopics}
          currentPage={currentPage}
          changePage={handleChangePage}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          totalPages={totalPages}
          loading={isLoading}
          error={isError}
          errorMessage={errorMessage}
          showCheckboxes={false}
          showSearch={true}
          showNameFilter={false}
          showSortFilter={true}
          showCreateButton={false}
          hideTitle={true}
          actions={actions}
          columns={columns}
        />
      </div>
    </div>
  );
};

export default NovelTopicList;