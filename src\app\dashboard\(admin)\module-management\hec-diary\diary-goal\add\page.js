'use client';
import NewTablePage from "@/components/form/NewTablePage";
import Link from 'next/link';
import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import FormInput from '@/components/form/FormInput';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';

const createStage = () => {
  const router = useRouter();

  const validationSchema = Yup.object().shape({
    words: Yup.string().required('Minimum Word is required'),
  });

  const initialValues = {
    words: '', // Fixed: changed from minimum_word to words to match validation
  };

  const handleSubmit = (values, { setSubmitting }) => {
    console.log(values);
    setSubmitting(false);
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className='min-h-screen bg-white p-5'>
      <div className="flex items-center gap-3 mb-3">
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-8 h-8 rounded-full transition-colors"
          aria-label="Go back"
        >
          <svg 
            className="w-4 h-4 text-gray-600" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 19l-7-7 7-7" 
            />
          </svg>
        </button>
        <h1 className="card-title text-black text-xl">Create Stage</h1>
      </div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors, touched, resetForm, values }) => (
          <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex-1 min-w-[200px]">
                <label htmlFor="words" className="block font-bold">
                  Default Stage
                </label>
                <FormInput
                  type="number"
                  name="words"
                  id="words" // Fixed: removed extra space
                  placeholder="0 Words"
                />
              </div>
            </div>
            
            {/* Moved buttons inside the Form component */}
            <div className="flex justify-end gap-4 mt-7">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  console.log('Cancel clicked, resetting form');
                  resetForm();
                }}
                className="bg-gray-300 hover:bg-gray-50 text-black font-medium py-2 px-4 rounded"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-[#FFDE34] hover:bg-yellow-300 text-black font-medium py-2 px-4 rounded disabled:opacity-50"
              >
                Save Changes
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default createStage;