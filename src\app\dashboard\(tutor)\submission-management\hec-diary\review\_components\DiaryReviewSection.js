'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import ContentEditable from 'react-contenteditable'; // Import the library
import api from '@/lib/api';
import { formatDate } from '@/utils/dateFormatter';
import FeedbackModal from './FeedbackModal';
import { useSelector, useDispatch } from 'react-redux';
import { selectIsSaving, setIsSaving } from '@/store/features/diarySlice';

// Helper to check if HTML content is effectively empty
const isHtmlEmpty = (html) => {
  if (!html) return true;
  const strippedHtml = html.replace(/<[^>]*>?/gm, '').trim();
  return strippedHtml.length === 0;
};

const DiaryReviewSection = ({ data, entryId }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  // State for the HTML content of the contentEditable div
  const [correctionHtml, setCorrectionHtml] = useState('');
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const contentEditableRef = useRef(null); // Ref for the ContentEditable component
  // Initialize or update correctionHtml when data.content changes
  const isCorrectionReviewed = !!data?.correction?.correctionText;
  const isSaving = useSelector(selectIsSaving);

  useEffect(() => {
    // Only update from data.content if it's different from current editor content
    // to prevent overwriting user edits if data.content prop reference changes unnecessarily.
    // This prioritizes prop change for initial load or new entry.
    if (data?.content && data.content !== correctionHtml) {
      setCorrectionHtml(data.content);
    } else if (!data?.content && correctionHtml !== '') {
      // If data.content is cleared, clear the editor too (unless already empty)
      setCorrectionHtml('');
    }
  }, [data?.content, correctionHtml]); // Rerun when data.content or correctionHtml changes

  const handleCorrectionChange = (evt) => {
    setCorrectionHtml(evt.target.value);
  };

  // Function to set the typing color to blue
  const prepareToTypeBlue = () => {
    // Ensure the editor has focus before executing the command
    if (contentEditableRef.current) {
      // contentEditableRef.current.focus(); // Focusing might be disruptive if already focused
      // or if user clicked to position cursor.
      // execCommand should work on the current selection/caret.
    }
    document.execCommand('foreColor', false, 'blue');
  };

  const submitReview = async () => {
    try {
      dispatch(setIsSaving(true));
      const response = await api.post(
        `/tutor/diary/entries/${entryId}/correction`,
        {
          correctionText: correctionHtml, // Send the HTML content
          score: parseInt(score),
        }
      );

      if (response.success) {
        // toast.success('Review submitted successfully');
        router.push('/dashboard/submission-management/hec-diary');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    } finally {
      dispatch(setIsSaving(false));
    }
  };

  const completeReview = async () => {
    try {
      dispatch(setIsSaving(true));
      const response = await api.post(
        `/tutor/diary/entries/${entryId}/complete-review`
      );

      if (response.success) {
        // toast.success('Review completed successfully');
        router.push('/dashboard/submission-management/hec-diary');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to complete review');
    } finally {
      dispatch(setIsSaving(false));
    }
  };

  return (
    <>
      <div className="p-2 shadow-xl h-full bg-white">
        {/* Original Content (remains the same) */}
        <div className="mb-4 rounded-md shadow-lg p-4 h-[204px] overflow-y-auto">
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Original Content</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>
          <p className="whitespace-pre-wrap text-sm text-[#314158]">
            {data.content} {/* Displaying original plain text content */}
          </p>
        </div>

        {/* Correction Section */}
        <div className="h-[400px] overflow-auto shadow-lg p-4 rounded-md">
          <p className="text-sm text-[#864D0D] text-center font-medium">
            Tutor Review Zone
          </p>
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Make Corrections</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>

          {data?.correction?.correctionText ? (
            <div
              className="mb-4 p-3 border border-gray-300 rounded-lg min-h-[9rem] overflow-y-auto whitespace-pre-wrap"
              dangerouslySetInnerHTML={{
                __html: data.correction.correctionText,
              }}
            />
          ) : (
            <div className="mb-4">
              <ContentEditable
                innerRef={contentEditableRef}
                html={correctionHtml}
                disabled={false}
                onChange={handleCorrectionChange}
                onFocus={prepareToTypeBlue}
                onClick={prepareToTypeBlue}
                onKeyDown={(e) => {
                  if (
                    e.key.length === 1 ||
                    e.key === 'Enter' ||
                    e.key === 'Backspace' ||
                    e.key === 'Delete'
                  ) {
                    prepareToTypeBlue();
                  }
                }}
                className="w-full p-3 border border-gray-300 rounded-lg min-h-[9rem] focus:outline-none focus:ring-2 focus:ring-yellow-300 editable-content"
                data-placeholder="Make corrections here..."
              />
              <style jsx global>{`
                .editable-content:empty::before {
                  content: attr(data-placeholder);
                  color: #a0aec0; /* Tailwind gray-500 */
                  pointer-events: none; /* Ensure placeholder doesn't interfere with clicks */
                }
              `}</style>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-5">
              <button
                onClick={() => setIsFeedbackModalOpen(true)}
                className="px-4 py-1 bg-[#FEFCE8] text-base text-[#723F11] rounded-md border border-[#723F11] font-medium hover:bg-[#FFF8D6]"
              >
                Give feedback
              </button>
              <div className="flex items-center border border-[#723F11] rounded-md overflow-hidden">
                <label className="px-3 py-1 bg-[#FEFCE8] text-[#723F11] text-base font-medium">
                  Score
                </label>
                {isCorrectionReviewed ? (
                  <div className="px-3 py-1 text-gray-700 bg-white">
                    {data.correction.score}
                  </div>
                ) : (
                  <input
                    type="number"
                    value={score}
                    onChange={(e) => setScore(e.target.value)}
                    className="w-16 px-3 py-1 focus:outline-none text-gray-700"
                    min="0"
                    max="100"
                    placeholder="---" // Changed placeholder to match image
                  />
                )}
              </div>
            </div>
            {isCorrectionReviewed ? (
              <div>
                <button
                  onClick={completeReview}
                  disabled={isSaving}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {isSaving ? "Saving..." : "Confirm"}
                </button>
              </div>
            ) : (
              <div>
                <button
                  onClick={submitReview}
                  disabled={isHtmlEmpty(correctionHtml) || !score || isSaving}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {isSaving ? "Saving..." : "Submit Review"}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        entryId={entryId}
      />
    </>
  );
};

export default DiaryReviewSection;
