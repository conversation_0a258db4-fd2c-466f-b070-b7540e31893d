import React from 'react';

const DiaryPage = ({ entry }) => {
  if (!entry) return null;

  // Format the date for better display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <div className="h-full flex flex-col p-6">
      {/* Header with title and date in a row with justify-between */}
      <div className="flex justify-between items-center mb-4 pb-3 border-b border-gray-200">
        <h2 className="text-xl font-bold text-[#1E3A8A]">{entry.title}</h2>
        <p className="text-sm text-gray-500">{formatDate(entry.entryDate)}</p>
      </div>

      {/* Content area */}
      <div className="flex-grow overflow-auto">
        <p className="text-gray-700 whitespace-pre-wrap">{entry.content}</p>
      </div>

      {/* Footer with status */}
      <div className="mt-4 pt-2">
        <p className="text-xs text-gray-400">
          {entry.status === 'draft' ? 'Draft' : 'Published'}
          {entry.skin && ` • ${entry.skin.name} skin`}
        </p>
      </div>
    </div>
  );
};

export default DiaryPage;
