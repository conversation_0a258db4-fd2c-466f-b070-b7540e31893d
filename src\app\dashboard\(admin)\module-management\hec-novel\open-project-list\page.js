'use client';

import NewTablePage from "@/components/form/NewTablePage";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';

const NovelTopicList = () => {
  const [activeTab, setActiveTab] = useState('questions'); // 'questions' or 'novelTopics'
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [questions, setQuestions] = useState([]);
  const [novelTopics, setNovelTopics] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [currentQuestionId, setCurrentQuestionId] = useState(null);
  const router = useRouter();
  
 

  // Fetch questions
  const fetchQuestions = async () => {
    try {
      setIsLoading(true);
      // Using the exact endpoints seen in the network requests
      const endpoint = activeTab === 'questions' 
        ? `/admin/qa/questions?page=${currentPage}&limit=${rowsPerPage}`
        : `/admin/qa/questions?page=${currentPage}&limit=${rowsPerPage}`;
      
      console.log(`Fetching from: ${endpoint}`);
      
      const response = await api.get(endpoint);
      console.log('Raw API response:', response);
      
      // Improved data extraction to handle different response structures
      if (response?.data) {
        let items = [];
        let totalCount = 0;
        let totalPagesCount = 0;
        
        // Check different possible data structures
        if (response.data.items && Array.isArray(response.data.items)) {
          // Direct items array at top level
          items = response.data.items;
          totalCount = response.data.totalCount || response.data.totalItems || 0;
          totalPagesCount = response.data.totalPages || 0;
        } else if (response.data.data) {
          // Items in nested data property
          if (response.data.data.items && Array.isArray(response.data.data.items)) {
            items = response.data.data.items;
            totalCount = response.data.data.totalCount || response.data.data.totalItems || 0;
            totalPagesCount = response.data.data.totalPages || 0;
          } else if (Array.isArray(response.data.data)) {
            // Direct array in data property
            items = response.data.data;
            totalCount = items.length;
            totalPagesCount = 1;
          }
        } else if (Array.isArray(response.data)) {
          // Response data is directly an array
          items = response.data;
          totalCount = items.length;
          totalPagesCount = 1;
        }
        
        if (items.length > 0) {
          if (activeTab === 'questions') {
            const formattedQuestions = items.map(item => ({
              id: item.id,
              title: item.question,
              points: item.points,
              minimumWords: item.minimumWords,
            }));
            
            setQuestions(formattedQuestions);
          } else {
            const formattedTopics = items.map(item => ({
              id: item.id,
              userName: item.userName || `${item.firstName || ''} ${item.lastName || ''}`.trim(),
              topicTitle: item.title || item.topic,
            }));
            
            setNovelTopics(formattedTopics);
          }
          
          setTotalItems(totalCount);
          setTotalPages(totalPagesCount);
          setIsError(false);
          setErrorMessage('');
        } else {
          console.log('No items found in response');
          if (activeTab === 'questions') {
            setQuestions([]);
          } else {
            setNovelTopics([]);
          }
          setTotalItems(0);
          setTotalPages(0);
          setIsError(false);
          setErrorMessage('');
        }
      } else {
        console.error('Unexpected data structure:', response);
        setIsError(true);
        setErrorMessage('Unexpected data structure received from API');
        if (activeTab === 'questions') {
          setQuestions([]);
        } else {
          setNovelTopics([]);
        }
      }
    } catch (err) {
      console.error(`Error fetching ${activeTab}:`, err);
      setIsError(true);
      setErrorMessage(err.message || `An error occurred while fetching ${activeTab}`);
      if (activeTab === 'questions') {
        setQuestions([]);
      } else {
        setNovelTopics([]);
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Reset to first page when changing tabs
    if (currentPage !== 1) {
      setCurrentPage(1);
    } else {
      // If already on page 1, manually trigger fetch since page didn't change
      fetchQuestions();
    }
  }, [activeTab]);

  useEffect(() => {
    fetchQuestions();
  }, [currentPage, rowsPerPage]);
  
  

  // View novel topic details
  const handleViewDetails = (row) => {
    // Implement view action logic here
    console.log('View details for:', row);
    // You might want to navigate to a detail page or open a modal
  };

  // Define columns for the question bank table
  const questionColumns = [
    {
      label: 'NOVEL PROJECT TITLE',
      field: 'userName',
    },
    {
      label: 'CREATED BY',
      field: 'topicTitle',
    },
  ];

  // Define columns for the novel topics table
  const novelTopicColumns = [
   
    {
      label: 'NOVEL PROJECT TITLE',
      field: 'userName',
    },
    {
      label: 'CREATED BY',
      field: 'topicTitle',
    },
  ];

  // Define actions
  const questionActions = [
    {
      name: 'edit',
      icon: 'material-symbols:edit',
      className: 'text-black-600',

    },
   
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',

    },

    
  ];

  const novelTopicActions = [
    {
      name: 'view',
      icon: 'heroicons-outline:eye',
      className: 'text-blue-600',
      onClick: handleViewDetails,
    },
    
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Open Project List</h1>
      
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <div className="flex">
          <button
            className={`px-6 py-3 font-medium text-sm ${
              activeTab === 'questions' 
                ? 'border-b-2 border-yellow-400 text-gray-900' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange('questions')}
            data-testid="topic-suggestion-tab"
          >
            Monthly Open Project List
          </button>
          <button
            className={`px-6 py-3 font-medium text-sm ${
              activeTab === 'novelTopics' 
                ? 'border-b-2 border-yellow-400 text-gray-900' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange('novelTopics')}
            data-testid="novel-topic-tab"
          >
           Quarterly Open Project List
          </button>
        </div>
      </div>
      
      
      
      {/* Content Area - Question Bank or Novel Topics */}
      <div className="bg-white rounded-lg shadow">
        {activeTab === 'questions' ? (
          <NewTablePage
            title=""
            createButton="Add Question"
            onCreateClick={() => router.push('/admin/questions/create')}
            columns={questionColumns}
            data={questions}
            actions={questionActions}
            currentPage={currentPage}
            changePage={handleChangePage}
            totalItems={totalItems}
            rowsPerPage={rowsPerPage}
            setRowsPerPage={setRowsPerPage}
            totalPages={totalPages}
            loading={isLoading}
            error={isError}
            errorMessage={errorMessage}
            showCheckboxes={false}
            showSearch={true}
            showNameFilter={false}
            showSortFilter={true}
            showCreateButton={false}
            hideTitle={true}
          />
        ) : (
          <NewTablePage
            title=""
            createButton="Add Novel Topic"
            onCreateClick={() => router.push('/admin/novel-topics/create')}
            columns={novelTopicColumns}
            data={novelTopics}
            actions={novelTopicActions}
            currentPage={currentPage}
            changePage={handleChangePage}
            totalItems={totalItems}
            rowsPerPage={rowsPerPage}
            setRowsPerPage={setRowsPerPage}
            totalPages={totalPages}
            loading={isLoading}
            error={isError}
            errorMessage={errorMessage}
            showCheckboxes={false}
            showSearch={true}
            showNameFilter={false}
            showSortFilter={true}
            showCreateButton={false}
            hideTitle={true}
          />
        )}
      </div>
      
      
    </div>
  );
};

export default NovelTopicList;