'use client';
import React, { useEffect } from 'react';
import Canvas from '../skin/Canvas';
import useDataFetch from '@/hooks/useDataFetch';
import { useDispatch } from 'react-redux';
import { setPreviewMode } from '@/store/features/canvasSlice';

const SharedDiarySection = () => {
  const dispatch = useDispatch();
  const { data, isLoading, error } = useDataFetch({
    queryKey: 'shared-entries',
    endPoint: 'diary/shared-entries?page=1&limit=10&sortBy=createdAt',
  });

  useEffect(() => {
    dispatch(setPreviewMode(true));
  }, [dispatch]);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const sharedEntries = data?.items || [];
  console.log('Shared entries:', sharedEntries);
  return (
    <div className="max-w-7xl mx-auto bg-white relative px-5 xl:px-0">
      <div className="py-10">
        <div className="mt-10 mb-7 w-full text-3xl font-semibold">
          Publicly Shared Diary
        </div>
      </div>
      <div className="flex justify-between flex-wrap gap-4">
        {sharedEntries.map((entry, index) => (
          <Canvas
            key={index}
            canvasItems={entry.items}
            canvasWidth={entry.width || '400px'}
            canvasHeight={entry.height || '300px'}
            canvasBackground={entry.background || '#f0f0f0'}
          />
        ))}
      </div>
    </div>
  );
};

export default SharedDiarySection;
