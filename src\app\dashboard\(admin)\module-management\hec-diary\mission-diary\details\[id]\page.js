'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';
import { Icon } from '@iconify/react';
import RegularGoBack from '@/components/shared/RegularGoBack';
import Link from 'next/link';

const MissionDetails = () => {
  const { id } = useParams();
  const router = useRouter();

  const {
    data: mission,
    isLoading,
    error,
  } = useDataFetch({
    queryKey: ['mission-diary-details', id],
    endPoint: `/diary/admin/missions/${id}`,
  });

  // Format date to readable format
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <RegularGoBack title="Mission Details" />
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </div>
    );
  }

  if (error || !mission) {
    return (
      <div className="p-6">
        <RegularGoBack title="Mission Details" />
        <div className="mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>Error loading mission data. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      <RegularGoBack title="Mission Details" />

      <div className="mt-6 bg-white rounded-lg shadow overflow-hidden border">
        {/* Header with mission title and actions */}
        <div className="bg-gray-50 p-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-800">{mission.title}</h1>

            <Link
              href={`/dashboard/module-management/hec-diary/mission-diary/edit/${mission.id}`}
              className="text-base text-gray-600 hover:underline"
            >
              <Icon icon={'material-symbols:edit-outline'} height={24} width={24} />
            </Link>
        </div>

        {/* Mission details */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Status */}
            <div className="col-span-1 md:col-span-2 flex justify-between items-center bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center">
                <Icon
                  icon={mission.isActive ? 'mdi:check-circle' : 'mdi:cancel'}
                  className={`w-6 h-6 mr-2 ${
                    mission.isActive ? 'text-green-500' : 'text-red-500'
                  }`}
                />
                <span className="font-medium">Status:</span>
              </div>
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  mission.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {mission.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            {/* Description */}
            <div className="col-span-1 md:col-span-2">
              <h2 className="text-lg font-semibold mb-2">Description</h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-700">{mission.description}</p>
              </div>
            </div>

            {/* Word Count */}
            <div>
              <h2 className="text-lg font-semibold mb-2">Word Count</h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Minimum:</span>
                  <span className="font-medium">
                    {mission.targetWordCount || 'Not specified'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Maximum:</span>
                  <span className="font-medium">
                    {mission.targetMaxWordCount || 'Not specified'}
                  </span>
                </div>
              </div>
            </div>

            {/* Dates */}
            <div>
              <h2 className="text-lg font-semibold mb-2">Timeline</h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Publish Date:</span>
                  <span className="font-medium">
                    {formatDate(mission.publishDate)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Expiry Date:</span>
                  <span className="font-medium">
                    {formatDate(mission.expiryDate)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MissionDetails;
